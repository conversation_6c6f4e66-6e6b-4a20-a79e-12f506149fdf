===============================================================================
                    COMO COMPILAR SEU MOD DO MINECRAFT
                         Passo a Passo Completo
===============================================================================

REQUISITOS NECESSÁRIOS:
-----------------------
✓ Java Development Kit (JDK) 8 ou superior instalado
✓ Variável de ambiente JAVA_HOME configurada
✓ Conexão com a internet (para baixar dependências)
✓ Pelo menos 2GB de espaço livre no disco

VERIFICAR SE JAVA ESTÁ INSTALADO:
---------------------------------
1. Abra o Prompt de Comando (cmd) ou PowerShell
2. Digite: java -version
3. Digite: javac -version
4. Se aparecer a versão, está tudo certo!

PASSO A PASSO PARA COMPILAR:
============================

MÉTODO 1 - USANDO O TERMINAL/PROMPT DE COMANDO:
-----------------------------------------------

1. ABRIR O TERMINAL:
   - Windows: Pressione Win + R, digite "cmd" e pressione Enter
   - Ou abra o PowerShell
   - Ou use o terminal integrado do VS Code (Ctrl + `)

2. NAVEGAR ATÉ A PASTA DO MOD:
   cd C:\Users\<USER>\OneDrive\Área de Trabalho\Projetos VsCode\mods minecraft\RubyMod

3. LIMPAR BUILDS ANTERIORES (OPCIONAL):
   .\gradlew clean

4. COMPILAR O MOD:
   .\gradlew build

5. AGUARDAR A COMPILAÇÃO:
   - O processo pode demorar alguns minutos na primeira vez
   - Você verá várias mensagens de progresso
   - Aguarde até aparecer "BUILD SUCCESSFUL"

6. LOCALIZAR O MOD COMPILADO:
   - O arquivo .jar estará em: build\libs\
   - Nome do arquivo: meumod-1.0.jar (ou similar)

MÉTODO 2 - USANDO O VS CODE:
---------------------------

1. ABRIR O TERMINAL INTEGRADO:
   - Pressione Ctrl + ` (crase)
   - Ou vá em Terminal > New Terminal

2. EXECUTAR O COMANDO DE BUILD:
   .\gradlew build

3. AGUARDAR A CONCLUSÃO

COMANDOS ÚTEIS DO GRADLE:
========================

.\gradlew clean          - Limpa arquivos de build anteriores
.\gradlew build          - Compila o mod completo
.\gradlew jar            - Cria apenas o arquivo JAR
.\gradlew runClient      - Executa o Minecraft com seu mod para teste
.\gradlew runServer      - Executa um servidor de teste
.\gradlew genEclipseRuns - Gera configurações para Eclipse
.\gradlew genIntellijRuns - Gera configurações para IntelliJ

SOLUÇÃO DE PROBLEMAS COMUNS:
============================

ERRO: "Java not found"
SOLUÇÃO: Instale o JDK e configure JAVA_HOME

ERRO: "Permission denied"
SOLUÇÃO: Execute como administrador ou use: chmod +x gradlew

ERRO: "Could not resolve dependencies"
SOLUÇÃO: Verifique sua conexão com internet

ERRO: "Build failed"
SOLUÇÃO: 
1. Execute: .\gradlew clean
2. Depois: .\gradlew build
3. Verifique se há erros no código

ERRO: "Out of memory"
SOLUÇÃO: Feche outros programas ou aumente a RAM do Gradle

ONDE ENCONTRAR O MOD COMPILADO:
===============================

Após a compilação bem-sucedida, seu mod estará em:
📁 build\libs\meumod-1.0.jar

Para usar o mod:
1. Copie o arquivo .jar
2. Cole na pasta "mods" do seu Minecraft com Forge
3. Execute o Minecraft

ESTRUTURA DE ARQUIVOS APÓS BUILD:
=================================

build\
├── libs\                    ← SEU MOD COMPILADO AQUI!
│   └── meumod-1.0.jar      ← Este é o arquivo final
├── classes\                 ← Classes Java compiladas
├── resources\               ← Recursos processados
└── tmp\                     ← Arquivos temporários

DICAS IMPORTANTES:
==================

✓ Sempre execute "gradlew clean" antes de "gradlew build" se houver problemas
✓ A primeira compilação demora mais (baixa dependências)
✓ Mantenha uma conexão estável com internet
✓ Verifique se não há erros no código antes de compilar
✓ O arquivo .jar final é o que você distribui/instala

EXEMPLO DE SAÍDA SUCCESSFUL:
============================

BUILD SUCCESSFUL in 2m 15s
7 actionable tasks: 7 executed

Se você ver esta mensagem, seu mod foi compilado com sucesso!

===============================================================================
                           FIM DO GUIA
===============================================================================
